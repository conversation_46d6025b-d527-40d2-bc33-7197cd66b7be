import {
  APPROVAL_STATUS,
  FILTER_TAKE,
  NOTIFICATION_TYPES,
  PROJECT_STATUS,
} from "@/libs/constants";
import { filterSchema } from "@/schemas/api.schemas";
import {
  ApprovalFilterSchema,
  ProjectApprovalRequestSchema,
  ProjectApprovalReviewSchema,
} from "@/schemas/approval.schemas";
import { getAllApprovals } from "@prisma/client/sql";
import { TRPCError } from "@trpc/server";
import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "../trpc";

export const approvalsRouter = createTRPCRouter({
  // Submit project for approval
  submitForApproval: protectedProcedure
    .input(ProjectApprovalRequestSchema)
    .mutation(async ({ ctx, input }) => {
      const userId = ctx.session.user.id;

      // Check if project exists and user has permission
      const project = await ctx.db.project.findUnique({
        where: { id: input.projectId },
        include: { organization: { include: { members: true } } },
      });

      if (!project) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Project not found",
        });
      }

      // Check if there's already a pending approval
      const existingApproval = await ctx.db.projectApproval.findFirst({
        where: {
          projectId: input.projectId,
          status: APPROVAL_STATUS.PENDING,
        },
      });

      if (existingApproval) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "Project already has a pending approval request",
        });
      }

      // Create approval request
      const approval = await ctx.db.projectApproval.create({
        data: {
          projectId: input.projectId,
          requesterId: userId,
          comments: input.comments,
          status: APPROVAL_STATUS.PENDING,
        },
        include: {
          project: true,
          requester: true,
        },
      });

      // Update project with dates, locations, and status
      const updateData: any = {
        status: PROJECT_STATUS.PENDING_APPROVAL,
      };

      if (input.startDate) {
        updateData.startDate = new Date(input.startDate);
      }
      if (input.endDate) {
        updateData.endDate = new Date(input.endDate);
      }

      await ctx.db.project.update({
        where: { id: input.projectId },
        data: updateData,
      });

      // Update project locations if provided
      if (input.locations && input.locations.length > 0) {
        await ctx.db.project.update({
          where: { id: input.projectId },
          data: {
            locations: {
              set: input.locations.map((locationId) => ({ id: locationId })),
            },
          },
        });
      }

      // Update project sublocations if provided
      if (input.sublocations && input.sublocations.length > 0) {
        await ctx.db.project.update({
          where: { id: input.projectId },
          data: {
            sublocations: {
              set: input.sublocations.map((sublocationId) => ({
                id: sublocationId,
              })),
            },
          },
        });
      }

      // Create notifications for organization admins/approvers
      const admins = project.organization!.members.filter(
        (member) => member.role === "admin" && member.userId !== userId,
      );

      if (admins.length > 0) {
        await ctx.db.approvalNotification.createMany({
          data: admins.map((admin) => ({
            approvalId: approval.id,
            userId: admin.userId,
            type: NOTIFICATION_TYPES.APPROVAL_REQUESTED,
          })),
        });
      }

      return approval;
    }),

  // Review approval (approve/reject)
  reviewApproval: protectedProcedure
    .input(ProjectApprovalReviewSchema)
    .mutation(async ({ ctx, input }) => {
      const userId = ctx.session.user.id;

      const approval = await ctx.db.projectApproval.findUnique({
        where: { id: input.approvalId },
        include: {
          project: {
            include: { organization: { include: { members: true } } },
          },
          requester: true,
        },
      });

      if (!approval) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Approval request not found",
        });
      }

      if (approval.status !== APPROVAL_STATUS.PENDING) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "Approval request has already been reviewed",
        });
      }

      // Check if user has permission to approve (admin role)
      const userMembership = approval.project.organization?.members.find(
        (member) => member.userId === userId,
      );

      if (!userMembership || userMembership.role !== "admin") {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You don't have permission to review this approval",
        });
      }

      // Update approval
      const updatedApproval = await ctx.db.projectApproval.update({
        where: { id: input.approvalId },
        data: {
          status: input.status,
          approverId: userId,
          reviewedAt: new Date(),
          reviewComments: input.reviewComments,
        },
        include: {
          project: true,
          requester: true,
          approver: true,
        },
      });

      // Update project status based on approval decision
      const newProjectStatus =
        input.status === APPROVAL_STATUS.APPROVED
          ? PROJECT_STATUS.APPROVED // Changed from PROJECT_STATUS.ACTIVE
          : PROJECT_STATUS.EDITING;

      await ctx.db.project.update({
        where: { id: approval.projectId },
        data: { status: newProjectStatus },
      });

      // Create notification for requester
      await ctx.db.approvalNotification.create({
        data: {
          approvalId: input.approvalId,
          userId: approval.requesterId,
          type:
            input.status === APPROVAL_STATUS.APPROVED
              ? NOTIFICATION_TYPES.APPROVED
              : NOTIFICATION_TYPES.REJECTED,
        },
      });

      return updatedApproval;
    }),

  // Get all approvals for organization
  getAll: protectedProcedure
    .input(
      z
        .object({
          organizationId: z.string(),
          ...filterSchema,
        })
        .merge(ApprovalFilterSchema),
    )
    .query(async ({ ctx, input }) => {
      const take = input?.take || FILTER_TAKE;

      const searchString = (input?.searchString || null) as string; // Doing this to get around type issues
      const status = (
        input.status === "all" ? null : input.status || null
      ) as string; // Doing this to get around type issues
      const projectId = (input.projectId || null) as string; // Doing this to get around type issues
      const cursor = (input.cursor || null) as string; // Doing this to get around type issues

      // Raw Typed Query
      // Using getAllApprovals SQL function to fetch approvals with pagination and filtering
      // This function is defined in the Prisma SQL directory file
      const data = await ctx.db.$queryRawTyped(
        getAllApprovals(
          input.organizationId,
          searchString,
          searchString, // Second param for the LIKE clause
          searchString, // Third param for the LIKE clause
          status,
          status, // Second param for status check
          status, // Third param for status comparison
          projectId,
          projectId, // Second param for projectId
          cursor,
          cursor, // Second param for cursor comparison
          take,
        ),
      );

      // Transform the data to match your expected format
      const transformedData = data.map((row) => ({
        id: row.id,
        projectId: row.projectId,
        requesterId: row.requesterId,
        approverId: row.approverId,
        status: row.status,
        submittedAt: row.submittedAt,
        reviewedAt: row.reviewedAt,
        comments: row.comments,
        reviewComments: row.reviewComments,
        createdAt: row.createdAt,
        updatedAt: row.updatedAt,
        project: {
          id: row.project_id,
          name: row.project_name,
          status: row.project_status,
          _count: { slides: Number(row.slide_count) },
        },
        requester: {
          id: row.requester_id,
          name: row.requester_name,
          email: row.requester_email,
          image: row.requester_image,
        },
        approver: row.approver_id
          ? {
              id: row.approver_id,
              name: row.approver_name,
              email: row.approver_email,
              image: row.approver_image,
            }
          : null,
      }));

      const result = { data: transformedData, cursor: "" };
      if (transformedData.length < take) return result;
      return { ...result, cursor: transformedData.at(-1)?.id || "" };
    }),

  // Get pending approvals that need user's review
  getPendingReviews: protectedProcedure
    .input(z.object({ organizationId: z.string() }))
    .query(async ({ ctx, input }) => {
      const userId = ctx.session.user.id;

      // Check if user is admin in the organization
      const membership = await ctx.db.member.findFirst({
        where: {
          userId,
          organizationId: input.organizationId,
          role: "admin",
        },
      });

      if (!membership) {
        return [];
      }

      return await ctx.db.projectApproval.findMany({
        where: {
          project: { organizationId: input.organizationId },
          status: APPROVAL_STATUS.PENDING,
        },
        include: {
          project: {
            select: {
              id: true,
              name: true,
              status: true,
              _count: { select: { slides: true } },
            },
          },
          requester: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true,
            },
          },
        },
        orderBy: { createdAt: "desc" },
      });
    }),

  // Get approval by ID
  getById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      return await ctx.db.projectApproval.findUnique({
        where: { id: input.id },
        include: {
          project: {
            include: {
              _count: { select: { slides: true } },
              slides: {
                take: 5,
                orderBy: { order: "asc" },
              },
            },
          },
          requester: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true,
            },
          },
          approver: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true,
            },
          },
        },
      });
    }),
});
