import { useState } from "react";

export interface UseClipboardOptions {
  /** Time in ms after which the copied state will reset, `2000` by default */
  timeout?: number;
}

export interface UseClipboardReturnValue {
  /** Function to copy value to clipboard */
  copy: (value: any) => void;

  /** Function to reset copied state and error */
  reset: () => void;

  /** Error if copying failed */
  error: Error | null;

  /** <PERSON>olean indicating if the value was copied successfully */
  copied: boolean;
}

/**
 * A React hook for managing clipboard operations with a timeout for the "copied" state.
 *
 * @param options - Configuration options for the clipboard hook
 * @param options.timeout - Duration in milliseconds before resetting the copied state (default: 2000ms)
 *
 * @returns An object containing:
 * - copy: (valueToCopy: string) => void - Function to copy text to clipboard
 * - reset: () => void - Function to reset the clipboard state
 * - error: Error | null - Any error that occurred during clipboard operations
 * - copied: boolean - Whether the text was successfully copied
 *
 * @example
 * ```tsx
 * const { copy, copied, error } = useClipboard({ timeout: 3000 });
 *
 * return (
 *   <button onClick={() => copy("Text to copy")}>
 *     {copied ? "Copied!" : "Copy"}
 *   </button>
 * );
 * ```
 *
 * @throws {Error} When navigator.clipboard is not supported in the browser
 */
export function useClipboard(
  options: UseClipboardOptions = { timeout: 2000 },
): UseClipboardReturnValue {
  const [error, setError] = useState<Error | null>(null);
  const [copied, setCopied] = useState(false);
  const [copyTimeout, setCopyTimeout] = useState<number | null>(null);

  const handleCopyResult = (value: boolean) => {
    window.clearTimeout(copyTimeout!);
    setCopyTimeout(window.setTimeout(() => setCopied(false), options.timeout));
    setCopied(value);
  };

  const copy = (value: any) => {
    if ("clipboard" in navigator) {
      navigator.clipboard
        .writeText(value)
        .then(() => handleCopyResult(true))
        .catch((err) => setError(err));
    } else {
      setError(new Error("useClipboard: navigator.clipboard is not supported"));
    }
  };

  const reset = () => {
    setCopied(false);
    setError(null);
    window.clearTimeout(copyTimeout!);
  };

  return { copy, reset, error, copied };
}
