import { ProjectSlideshow } from "@/components/projects/project-slideshow";
import { getSlides } from "@/server/actions/get-slides";

export interface Props {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}

export default async function PreviewPage(props: Props) {
  const searchParams = await props.searchParams;
  const projectId = searchParams?.projectId as string;
  const slides = await getSlides(projectId ?? "");

  return <ProjectSlideshow slides={slides} />;
}
