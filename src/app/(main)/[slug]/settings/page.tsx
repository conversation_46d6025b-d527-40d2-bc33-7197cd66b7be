// import { SettingsGeneralView } from "@/components/settings/settings-general-view";
// import { api, HydrateClient } from "@/trpc/server";

// interface Props {
//   searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
// }

// export default async function SettingsGeneralPage({ searchParams }: Props) {
//   const params = await searchParams;

//   void api.user.getUser.prefetch();
//   void api.user.getActiveSession.prefetch();

//   return (
//     <HydrateClient>
//       <SettingsGeneralView searchParams={params} />
//     </HydrateClient>
//   );
// }

import { SettingsOrganizationView } from "@/components/settings/organization/settings-organization-view";

interface Props {
  params: Promise<{ slug: string }>;
}

export default async function SettingsGeneralPage({ params }: Props) {
  const { slug } = await params;
  return <SettingsOrganizationView slug={slug} />;
}
