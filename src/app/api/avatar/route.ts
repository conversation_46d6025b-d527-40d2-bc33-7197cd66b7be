import { uploadFile } from "@/libs/s3";
import { createImageUploadUrl } from "@/utils/create-image-upload-url";
import {
  generateAvatarImage,
  generateGradientAvatarImage,
} from "@/utils/generate-avatar-image";
import { NextRequest, NextResponse } from "next/server";

export async function POST(req: NextRequest) {
  const resourceType = req.nextUrl.searchParams.get("resourceType") as string;

  const body = await req.json();
  const { seed, key } = body as {
    seed: string;
    key: string;
  };

  try {
    const imageBuffer =
      resourceType === "organization"
        ? generateGradientAvatarImage(seed)
        : generateAvatarImage(seed);
    await uploadFile(key, imageBuffer);
    const url = createImageUploadUrl(key);
    return NextResponse.json({ url }, { status: 201 });
  } catch (e) {
    return NextResponse.json(
      { error: "Failed to generate avatar" },
      { status: 500 },
    );
  }
}
