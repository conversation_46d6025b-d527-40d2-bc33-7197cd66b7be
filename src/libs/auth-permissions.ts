import { createAccessControl } from "better-auth/plugins/access";
import {
  adminAc,
  defaultStatements,
  memberAc,
} from "better-auth/plugins/organization/access";

/**
 * make sure to use `as const` so typescript can infer the type correctly
 */
const statement = {
  ...defaultStatements,
  project: ["read", "create", "update", "delete"],
  organization: ["read", "update", "delete"],
} as const;

export const ac = createAccessControl(statement);

export const admin = ac.newRole({
  ...adminAc.statements,
  project: ["create", "update", "delete"],
});

export const manager = ac.newRole({
  ...memberAc.statements,
  project: ["create", "update", "delete"],
  organization: ["read"],
});

export const viewer = ac.newRole({
  project: ["read"],
  organization: ["read"],
});
