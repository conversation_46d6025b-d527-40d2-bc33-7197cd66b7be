/**
 * Checks if a value is a symbol.
 * @param {*} value - The value to check.
 * @returns {boolean} True if the value is a symbol.
 */
export const isSymbol = (value: any): value is symbol => {
  return !!value && value.constructor === Symbol;
};

/**
 * Checks if a value is an array.
 * @param {*} value - The value to check.
 * @returns {boolean} True if the value is an array.
 */
export const isArray = Array.isArray;

/**
 * Checks if a value is a plain object.
 * @param {*} value - The value to check.
 * @returns {boolean} True if the value is an object.
 */
export const isObject = (value: any): value is object => {
  return !!value && value.constructor === Object;
};

/**
 * Checks if the given value is primitive.
 * Primitive Types: number, string, boolean, symbol, bigint, undefined, null.
 * @param {*} value - The value to check.
 * @returns {boolean} True if the value is a primitive.
 */
export const isPrimitive = (value: any): boolean => {
  return (
    value === undefined ||
    value === null ||
    (typeof value !== "object" && typeof value !== "function")
  );
};

/**
 * Checks if the given value is a function.
 * @param {*} value - The value to check.
 * @returns True if the value is a function.
 */
export const isFunction = (value: unknown) => {
  return typeof value === "function" || value instanceof Function;
};
/**
 * Checks if the given value is a string.
 * @param {*} value - The value to check.
 * @returns {boolean} True if the value is a string.
 */
export const isString = (value: any): value is string => {
  return typeof value === "string" || value instanceof String;
};

/**
 * Checks if the given value is an integer.
 * @param {*} value - The value to check.
 * @returns {boolean} True if the value is an integer.
 */
export const isInt = (value: any): value is number => {
  return isNumber(value) && value % 1 === 0;
};

/**
 * Checks if the given value is a float.
 * @param {*} value - The value to check.
 * @returns {boolean} True if the value is a float.
 */
export const isFloat = (value: any): value is number => {
  return isNumber(value) && value % 1 !== 0;
};

/**
 * Checks if the given value is a number.
 * @param {*} value - The value to check.
 * @returns {boolean} True if the value is a number.
 */
export const isNumber = (value: any): value is number => {
  try {
    return Number(value) === value;
  } catch {
    return false;
  }
};

/**
 * Checks if the given value is a Date instance.
 * @param {*} value - The value to check.
 * @returns {boolean} True if the value is a Date.
 */
export const isDate = (value: any): value is Date => {
  return Object.prototype.toString.call(value) === "[object Date]";
};

/**
 * Checks if the given value is considered empty.
 * @param {*} value - The value to check.
 * @returns True if the value is empty.
 */
export const isEmpty = (value: any) => {
  if (value === true || value === false) return true;
  if (value === null || value === undefined) return true;
  if (isNumber(value)) return value === 0;
  if (isDate(value)) return isNaN(value.getTime());
  if (isFunction(value)) return false;
  if (isSymbol(value)) return false;
  const length = (value as any).length;
  if (isNumber(length)) return length === 0;
  const size = (value as any).size;
  if (isNumber(size)) return size === 0;
  const keys = Object.keys(value).length;
  return keys === 0;
};
