import { isArray, isDate } from "@/utils/typed";

// TYPE SAFETY: Define union type for all comparable values
type Comparable =
  | null
  | undefined
  | boolean
  | number
  | string
  | Date
  | Comparable[]
  | { [key: string]: Comparable };

// TYPE SAFETY: Function overloads for better type inference
export function isEqual<T extends Comparable>(a: T, b: T): boolean;
export function isEqual(a: unknown, b: unknown): boolean;

/**
 * Performs deep equality comparison between two values with optimized performance
 * @param a - First value to compare
 * @param b - Second value to compare
 * @returns true if values are deeply equal, false otherwise
 */
export function isEqual(a: unknown, b: unknown): boolean {
  // PERFORMANCE: Fastest check - strict equality (same reference or primitive equality)
  // Handles: same object reference, same primitive values, both null, both undefined
  if (a === b) return true;

  // PERFORMANCE: Early type mismatch detection - avoids expensive object traversal
  // Note: typeof null === 'object', so this catches most type mismatches efficiently
  if (typeof a !== typeof b) return false;

  // PERFORMANCE: Quick null/undefined check after type comparison
  // Since typeof null === 'object', we need explicit null checks
  // TYPE SAFETY: Explicit null/undefined type guards
  if (a == null || b == null) return false; // One is null/undefined, other isn't

  // TYPE SAFETY: Date type guard with instanceof check
  if (isDate(a) && isDate(b)) {
    return a.getTime() === b.getTime();
  }

  // TYPE SAFETY: Object type guard before deep comparison - includes arrays
  if (typeof a === "object" && typeof b === "object") {
    return deepEqual(a, b);
  }

  // PERFORMANCE: All primitive types handled above, this should rarely execute
  return false;
}

/**
 * Deep equality comparison for objects and arrays with performance optimizations
 * @param a - First object/array to compare
 * @param b - Second object/array to compare
 * @returns true if deeply equal, false otherwise
 */
function deepEqual(a: object, b: object): boolean {
  // TYPE SAFETY: Type-safe array checks with type guards
  const aIsArray = isArray(a);
  const bIsArray = isArray(b);

  // PERFORMANCE: Early return for type mismatch (array vs object)
  if (aIsArray !== bIsArray) return false;

  if (aIsArray && bIsArray) {
    // TYPE SAFETY: TypeScript now knows both are arrays
    // PERFORMANCE: Length check before iterating - O(1) operation
    // Avoids unnecessary element-by-element comparison for different-sized arrays
    if (a.length !== b.length) return false;

    // PERFORMANCE: Simple for loop (faster than forEach/map for early termination)
    // Exits immediately on first inequality without checking remaining elements
    for (let i = 0; i < a.length; i++) {
      if (!isEqual(a[i], b[i])) return false;
    }

    return true;
  }

  // TYPE SAFETY: TypeScript now knows both are objects (not arrays)
  if (!aIsArray && !bIsArray) {
    // TYPE SAFETY: Cast to Record after confirming they're objects, not arrays
    const objA = a as Record<string, unknown>;
    const objB = b as Record<string, unknown>;

    // PERFORMANCE: Object.keys() called once per object and cached
    // More efficient than Object.entries() since we only need keys
    const keysA = Object.keys(objA);
    const keysB = Object.keys(objB);

    // PERFORMANCE: Length comparison before key iteration - O(1) operation
    // Eliminates need for expensive key-by-key comparison for different-sized objects
    if (keysA.length !== keysB.length) return false;

    // PERFORMANCE: Single loop with hasOwnProperty check built into Object.keys()
    // More efficient than checking hasOwnProperty separately
    for (let i = 0; i < keysA.length; i++) {
      const key = keysA[i] as string;

      // TYPE SAFETY: Type-safe property access with proper key checking
      if (!Object.prototype.hasOwnProperty.call(objB, key)) return false;

      // PERFORMANCE: Recursive call with early termination
      // Stops comparison immediately on first property inequality
      if (!isEqual(objA[key], objB[key])) return false;
    }

    return true;
  }

  return false;
}
