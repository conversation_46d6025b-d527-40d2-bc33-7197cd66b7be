import { api } from "@/trpc/react";
import { isEmpty } from "@/utils/typed";
import { toast } from "sonner";

export function useSlideById(id: string) {
  return api.slides.getById.useQuery({ id: id }, { enabled: !isEmpty(id) });
}

export function useSlides(projectId: string) {
  return api.slides.getAll.useQuery(
    { projectId },
    { enabled: !isEmpty(projectId) },
  );
}

// export function useInfiniteSlides(
//   input?: SlidesFindInput,
//   initialData?: SlidesOutput,
// ) {
//   return api.slides.getAll.useInfiniteQuery(
//     { ...input },
//     {
//       initialData: () => {
//         if (initialData) {
//           return {
//             pageParams: [undefined],
//             pages: [initialData],
//           };
//         }
//       },
//       getNextPageParam: (lastPage) => lastPage?.cursor || undefined,
//     },
//   );
// }

export function useSlideAddMutation() {
  const utils = api.useUtils();

  return api.slides.create.useMutation({
    onError: (error, _input, ctx) => {
      console.log(error);
      toast.error("Error", { description: error.message });
    },
    onSettled: async () => {
      await utils.slides.getAll.invalidate();
    },
  });
}

export function useSlideAddManyMutation() {
  const utils = api.useUtils();

  return api.slides.createMany.useMutation({
    onError: (error, _input, ctx) => {
      console.log(error);
      toast.error("Error", { description: error.message });
    },
    onSettled: async () => {
      await utils.slides.getAll.invalidate();
    },
  });
}

export const useSlideUpdateMutation = () => {
  const utils = api.useUtils();

  return api.slides.updateById.useMutation({
    onMutate: async (input) => {
      console.log("Mutation input:", input);
      await utils.slides.getById.cancel({ id: input.id });
      const previousQueryData = utils.slides.getById.getData({
        id: input.id,
      });
      return { previousQueryData };
    },
    onError: (error, input, ctx) => {
      console.error("Mutation error:", error);
      utils.slides.getById.setData({ id: input.id }, ctx?.previousQueryData);
      toast.error("Error", { description: error.message });
    },
    onSuccess: (data) => {
      console.log("Mutation success:", data);
    },
    onSettled: async (_data, _error, input) => {
      await utils.slides.getById.invalidate({
        id: input.id,
      });
      await utils.slides.getAll.invalidate();
    },
  });
};

export const useSlideBatchUpdateMutation = () => {
  const utils = api.useUtils();

  return api.slides.batchUpdate.useMutation({
    onError: (error, _input, _ctx) => {
      console.log(error);
      toast.error("Error", { description: error.message });
    },
    onSettled: async () => {
      await utils.slides.getAll.invalidate();
    },
  });
};

export const useSlideDeleteMutation = () => {
  const utils = api.useUtils();

  return api.slides.deleteById.useMutation({
    onMutate: async (input) => {
      await utils.slides.getById.cancel({ id: input.id });
      const previousQueryData = utils.slides.getById.getData({
        id: input.id,
      });
      return { previousQueryData };
    },
    onError: (error, input, ctx) => {
      console.log(error);
      utils.slides.getById.setData({ id: input.id }, ctx?.previousQueryData);
      toast.error("Error", { description: error.message });
    },
    onSettled: async (_data, _error, input) => {
      await utils.slides.getById.invalidate({ id: input.id });
      await utils.slides.getAll.invalidate();
    },
  });
};
