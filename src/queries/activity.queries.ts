import { api } from "@/trpc/react";
import { isEmpty } from "@/utils/typed";

function useActivityProjects(input: { organizationId: string }) {
  return api.projects.getAllApprovedActiveAndCompleted.useQuery(
    { organizationId: input.organizationId },
    { enabled: !isEmpty(input.organizationId) },
  );
}

function useActivityStats(input: { organizationId: string }) {
  return api.projects.getActivityStats.useQuery(
    { organizationId: input.organizationId },
    { enabled: !isEmpty(input.organizationId) },
  );
}

function useProjectActivityTimeline(input: { organizationId: string }) {
  return api.projects.getProjectActivityTimeline.useQuery(
    { organizationId: input.organizationId },
    { enabled: !isEmpty(input.organizationId) },
  );
}

export { useActivityProjects, useActivityStats, useProjectActivityTimeline };
