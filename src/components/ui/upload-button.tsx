import { Default<PERSON>utton as But<PERSON> } from "@/components/ui/button";
import { IconLoader2, IconUpload } from "@tabler/icons-react";
import { useUploadFile } from "better-upload/client";
import { useId, useRef } from "react";

type UploadButtonProps = Parameters<typeof useUploadFile>[0] & {
  accept?: string;
  metadata?: Record<string, unknown>;

  // Add any additional props you need.
  variant?: "outline" | "ghost" | "default";
  text?: string;
};

export function UploadButton({
  accept,
  metadata,
  variant = "default",
  text = "Upload file",
  ...params
}: UploadButtonProps) {
  const id = useId();
  const inputRef = useRef<HTMLInputElement | null>(null);

  const { upload, isPending } = useUploadFile({
    ...params,
    onUploadSettled: () => {
      if (inputRef.current) {
        inputRef.current.value = "";
      }

      params.onUploadSettled?.();
    },
  });

  return (
    <Button
      disabled={isPending}
      className="relative"
      type="button"
      variant={variant}
    >
      <label htmlFor={id} className="absolute inset-0 cursor-pointer">
        <input
          id={id}
          ref={inputRef}
          className="absolute inset-0 size-0 opacity-0"
          type="file"
          accept={accept}
          onChange={(e) => {
            if (e.target.files?.[0]) {
              upload(e.target.files[0], { metadata });
            }
          }}
        />
      </label>
      {isPending ? (
        <>
          <IconLoader2 className="size-4 animate-spin" />
          {text}
        </>
      ) : (
        <>
          <IconUpload className="size-4" />
          {text}
        </>
      )}
    </Button>
  );
}
