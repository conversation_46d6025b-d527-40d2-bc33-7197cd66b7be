"use client";

import { ProjectStatusBadge } from "@/components/projects/project-status-badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { PROJECT_STATUS } from "@/libs/constants";
import { cn } from "@/libs/utils";
import type { ActiveProject, ProjectStatus } from "@/types/project.types";
import { formatDate } from "@/utils/format-date";
import {
  IconArrowRight,
  IconCalendarWeek,
  IconLocation,
  IconStack2,
} from "@tabler/icons-react";
import Link from "next/link";
import { useState } from "react";
import { ActivityCardDetailsSheet } from "./activity-card-details-sheet";

interface ActivityCardProps {
  project: ActiveProject;
  orgSlug: string;
}

export function ActivityCard({ project, orgSlug }: ActivityCardProps) {
  const [isSheetOpen, setIsSheetOpen] = useState(false);

  const cardBorderColors = {
    [PROJECT_STATUS.ACTIVE]: "border-t-green-400",
    [PROJECT_STATUS.APPROVED]: "border-t-blue-500",
    [PROJECT_STATUS.EDITING]: "border-t-gray-300",
    [PROJECT_STATUS.PENDING_APPROVAL]: "border-t-yellow-400",
    [PROJECT_STATUS.COMPLETED]: "border-t-orange-500",
  } as const;

  const projectStatus =
    (project.status as ProjectStatus) ?? PROJECT_STATUS.EDITING;

  return (
    <>
      <Card
        className={cn(
          `group relative overflow-hidden border-t-4 transition-all duration-200`,
          {
            [cardBorderColors[projectStatus]]: projectStatus,
          },
        )}
      >
        <CardHeader className="relative pb-3">
          <div className="flex items-start justify-between">
            <div className="flex-1 space-y-3">
              <div className="flex items-center gap-2">
                <ProjectStatusBadge status={project.status as ProjectStatus} />
              </div>
              <div>
                <Link href={`/${orgSlug}/projects/${project.id}`}>
                  <h3 className="text-xl leading-tight font-bold text-gray-900 transition-colors group-hover:text-gray-700">
                    {project.name}
                  </h3>
                </Link>
              </div>
            </div>
          </div>
        </CardHeader>

        <CardContent className="relative space-y-5">
          {/* Date Range */}
          {project.startDate && project.endDate && (
            <div className="flex items-center gap-3 text-sm text-gray-600">
              <div className="rounded-xl bg-blue-50 p-2">
                <IconCalendarWeek className="h-4 w-4 text-blue-600" />
              </div>
              <span className="font-medium text-gray-700">
                {formatDate(project.startDate)} - {formatDate(project.endDate)}
              </span>
            </div>
          )}

          {/* Stats Grid */}
          <div className="grid grid-cols-2 gap-4">
            <div className="rounded-xl border border-gray-200 bg-gradient-to-br from-gray-50 to-gray-100 p-4">
              <div className="mb-1 flex items-center gap-2 text-sm text-gray-500">
                <IconLocation className="h-4 w-4" />
                <span>Locations</span>
              </div>
              <div className="text-2xl font-bold text-gray-900">
                {project.locations?.length ?? 0}
              </div>
            </div>

            <div className="rounded-xl border border-gray-200 bg-gradient-to-br from-gray-50 to-gray-100 p-4">
              <div className="mb-1 flex items-center gap-2 text-sm text-gray-500">
                <IconStack2 className="h-4 w-4" />
                <span>Slides</span>
              </div>
              <div className="text-2xl font-bold text-gray-900">
                {project._count.slides}
              </div>
            </div>
          </div>

          {/* Locations Preview */}
          {project.locations && project.locations.length > 0 && (
            <div className="rounded-xl border border-gray-200 bg-gradient-to-br from-gray-50 to-gray-100 p-4">
              <div className="mb-3 text-sm font-semibold text-gray-700">
                Active Locations
              </div>
              <div className="space-y-2">
                {project.locations.slice(0, 2).map((location) => (
                  <div key={location.id} className="space-y-1">
                    <div className="flex items-center gap-3 text-sm text-gray-600">
                      <div className="h-2 w-2 rounded-full bg-green-400" />
                      <span className="font-medium">
                        {location.name} - {location.city}, {location.state}
                      </span>
                    </div>
                  </div>
                ))}
                {project.locations.length > 2 && (
                  <div className="ml-5 text-sm font-medium text-gray-500">
                    +{project.locations.length - 2} more locations
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Action Button */}
          <div className="pt-2">
            <Button
              variant="outline"
              className="w-full border-gray-200 font-medium hover:border-gray-300 hover:bg-gray-50"
              onClick={() => setIsSheetOpen(true)}
              rightIcon={<IconArrowRight size={16} />}
            >
              View details
            </Button>
          </div>
        </CardContent>
      </Card>

      <ActivityCardDetailsSheet
        open={isSheetOpen}
        onOpenChange={setIsSheetOpen}
        project={project}
        orgSlug={orgSlug}
      />
    </>
  );
}
