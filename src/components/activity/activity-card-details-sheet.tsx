"use client";

import { ProjectStatusBadge } from "@/components/projects/project-status-badge";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Sheet,
  Sheet<PERSON>ontent,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";
import { PROJECT_STATUS } from "@/libs/constants";
import type { ActiveProject, ProjectStatus } from "@/types/project.types";
import { formatDate } from "@/utils/format-date";
import {
  IconBuilding,
  IconCalendarWeek,
  IconClock,
  IconExternalLink,
  IconLocation,
  IconMapPin,
  IconPencil,
  IconPlayerPlay,
  IconStack2,
} from "@tabler/icons-react";
import { CheckCircle, Play } from "lucide-react";
import Link from "next/link";

interface Props {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  project: ActiveProject;
  orgSlug: string;
}

export function ActivityCardDetailsSheet({
  open,
  onOpenChange,
  project,
  orgSlug,
}: Props) {
  const projectStatus =
    (project.status as ProjectStatus) ?? PROJECT_STATUS.EDITING;

  const getStatusConfig = (status: ProjectStatus) => {
    switch (status) {
      case PROJECT_STATUS.ACTIVE:
        return {
          bg: "bg-green-50",
          text: "text-green-700",
          border: "border-green-200",
          icon: <Play className="h-3.5 w-3.5" />,
        };
      case PROJECT_STATUS.APPROVED:
        return {
          bg: "bg-emerald-50",
          text: "text-emerald-700",
          border: "border-emerald-200",
          icon: <CheckCircle className="h-3.5 w-3.5" />,
        };
      case PROJECT_STATUS.EDITING:
        return {
          bg: "bg-amber-50",
          text: "text-amber-700",
          border: "border-amber-200",
          icon: <IconPencil className="h-3.5 w-3.5" />,
        };
      default:
        return {
          bg: "bg-gray-50",
          text: "text-gray-700",
          border: "border-gray-200",
          icon: <IconPencil className="h-3.5 w-3.5" />,
        };
    }
  };

  const totalSublocations = project.sublocations?.length ?? 0;

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent className="w-full overflow-y-auto sm:max-w-lg">
        <SheetHeader className="space-y-4">
          <div className="space-y-2">
            <SheetTitle className="text-2xl font-bold">
              {project.name}
            </SheetTitle>
            <div className="flex items-center gap-2">
              <ProjectStatusBadge status={projectStatus} />
              <span className="text-sm text-gray-500">
                Last updated {formatDate(project.updatedAt)}
              </span>
            </div>
          </div>
        </SheetHeader>

        <div className="flex items-center space-x-2 px-4 pb-4">
          <Button
            variant="outline"
            href={`/${orgSlug}/projects/${project.id}`}
            leftIcon={<IconExternalLink className="size-4" />}
          >
            Open Project
          </Button>
          <Button
            variant="outline"
            href={`/secure/preview?projectId=${project.id}`}
            target="_blank"
            rel="noreferrer noopener"
            leftIcon={<IconPlayerPlay className="size-4" />}
          >
            View preview
          </Button>
        </div>

        <div className="space-y-6 px-4 pb-4">
          {/* Project Stats */}
          <div className="grid grid-cols-3 gap-4">
            <Card>
              <CardContent className="p-4 text-center">
                <div className="mb-2 flex justify-center">
                  <IconStack2 className="h-6 w-6 text-blue-600" />
                </div>
                <div className="text-2xl font-bold text-gray-900">
                  {project._count.slides}
                </div>
                <div className="text-sm text-gray-600">Slides</div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4 text-center">
                <div className="mb-2 flex justify-center">
                  <IconLocation className="h-6 w-6 text-green-600" />
                </div>
                <div className="text-2xl font-bold text-gray-900">
                  {project.locations?.length ?? 0}
                </div>
                <div className="text-sm text-gray-600">Locations</div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4 text-center">
                <div className="mb-2 flex justify-center">
                  <IconMapPin className="h-6 w-6 text-purple-600" />
                </div>
                <div className="text-2xl font-bold text-gray-900">
                  {totalSublocations}
                </div>
                <div className="text-sm text-gray-600">Sublocations</div>
              </CardContent>
            </Card>
          </div>

          {/* Project Runtime */}
          {project.startDate && project.endDate && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <IconCalendarWeek className="h-5 w-5" />
                  Project Runtime
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Date Range Display */}
                <div className="flex items-center justify-between">
                  <div className="flex flex-col items-center space-y-1">
                    <div className="flex h-10 w-10 items-center justify-center rounded-full bg-green-100">
                      <div className="h-3 w-3 rounded-full bg-green-600" />
                    </div>
                    <div className="text-center">
                      <div className="text-xs font-medium tracking-wide text-gray-500 uppercase">
                        Start Date
                      </div>
                      <div className="font-semibold text-gray-900">
                        {formatDate(project.startDate)}
                      </div>
                    </div>
                  </div>

                  {/* Progress Line */}
                  <div className="flex flex-1 items-center px-4">
                    <div className="relative h-px w-full bg-gray-200">
                      <div className="absolute top-0 left-0 h-px w-full bg-gradient-to-r from-green-600 to-blue-600" />
                      <div className="absolute top-1/2 left-1/2 h-3 w-3 -translate-x-1/2 -translate-y-1/2 rounded-full border-2 border-white bg-blue-600 shadow-sm" />
                    </div>
                  </div>

                  <div className="flex flex-col items-center space-y-1">
                    <div className="flex h-10 w-10 items-center justify-center rounded-full bg-blue-100">
                      <div className="h-3 w-3 rounded-full bg-blue-600" />
                    </div>
                    <div className="text-center">
                      <div className="text-xs font-medium tracking-wide text-gray-500 uppercase">
                        End Date
                      </div>
                      <div className="font-semibold text-gray-900">
                        {formatDate(project.endDate)}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Duration Badge */}
                <div className="flex justify-center">
                  <div className="flex items-center gap-2 rounded-lg border border-purple-200 bg-purple-50 px-3 py-1.5 text-xs font-medium text-purple-700">
                    <IconClock className="h-3.5 w-3.5" />
                    <span>
                      {Math.ceil(
                        (project.endDate.getTime() -
                          project.startDate.getTime()) /
                          (1000 * 60 * 60 * 24),
                      )}{" "}
                      days duration
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Active Locations */}
          {project.locations && project.locations.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <IconBuilding className="h-5 w-5" />
                  Active Locations ({project.locations.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {project.locations.map((location) => (
                    <div
                      key={location.id}
                      className="rounded-lg border border-gray-200 p-4"
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h4 className="font-semibold text-gray-900">
                            {location.name}
                          </h4>
                          <p className="text-sm text-gray-600">
                            {location.city}, {location.state}
                          </p>
                        </div>
                        {project.sublocations &&
                          project.sublocations.length > 0 && (
                            <div className="ml-2 flex items-center gap-2 rounded-lg border border-blue-200 bg-blue-50 px-3 py-1.5 text-xs font-medium text-blue-700">
                              <IconMapPin className="h-3.5 w-3.5" />
                              <span>
                                {project.sublocations.length} sublocation
                                {project.sublocations.length !== 1 ? "s" : ""}
                              </span>
                            </div>
                          )}
                      </div>

                      {/* Sublocations */}
                      {project.sublocations &&
                        project.sublocations.length > 0 && (
                          <div className="mt-3 space-y-2">
                            <div className="text-sm font-medium text-gray-700">
                              Sublocations:
                            </div>
                            <div className="grid grid-cols-2 gap-2">
                              {project.sublocations.map((sublocation) => (
                                <Link
                                  key={sublocation.id}
                                  href={`/secure/view?location=${sublocation.locationId}&sub-location=${sublocation.id}`}
                                  target="_blank"
                                  rel="noreferrer noopener"
                                  className="flex items-center gap-2 rounded-md bg-gray-50 px-3 py-2 text-sm"
                                >
                                  <div className="h-2 w-2 rounded-full bg-gray-400" />
                                  <span className="text-gray-700">
                                    {sublocation.name}
                                  </span>
                                </Link>
                              ))}
                            </div>
                          </div>
                        )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Actions */}
          {/* <SheetFooter className="flex-shrink-0 border-gray-200 px-0 pt-4">
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                href={`/${orgSlug}/projects/${project.id}`}
                leftIcon={<IconExternalLink className="size-4" />}
              >
                Open Project
              </Button>
              <Button
                variant="outline"
                href={`/secure/preview?projectId=${project.id}`}
                target="_blank"
                rel="noreferrer noopener"
                leftIcon={<IconPlayerPlay className="size-4" />}
              >
                View preview
              </Button>
            </div>
          </SheetFooter> */}
        </div>
      </SheetContent>
    </Sheet>
  );
}
