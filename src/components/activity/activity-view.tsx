"use client";

import { ActivityCard } from "@/components/activity/activity-card";
import { Card } from "@/components/ui/card";
import { EmptyState } from "@/components/ui/empty-state";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { PageContent, PageLoader } from "@/components/ui/page-structure";
import { PageTitle } from "@/components/ui/page-title";
import { PageWrapper } from "@/components/ui/page-wrapper";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { StatCard } from "@/components/ui/stat-card";
import { useOrganizationSlug } from "@/hooks/use-organization-slug";
import {
  useActivityProjects,
  useActivityStats,
} from "@/queries/activity.queries";
import { useLocations } from "@/queries/location.queries";
import { useOrganizationBySlug } from "@/queries/organization.queries";
import { isEmpty } from "@/utils/is-empty";
import {
  IconActivity,
  IconCalendarCheck,
  IconCalendarWeek,
  IconFolder,
  IconLocation,
  IconPlayerPlay,
  IconSearch,
  IconTrendingUp,
} from "@tabler/icons-react";
import { CheckCircle } from "lucide-react";
import { useMemo, useState } from "react";

export function ActivityView() {
  const slug = useOrganizationSlug();
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [locationFilter, setLocationFilter] = useState<string>("all");
  const [sublocationFilter, setSublocationFilter] = useState<string>("all");

  const organization = useOrganizationBySlug(slug);
  const activityQuery = useActivityProjects({
    organizationId: organization?.data?.id ?? "",
  });
  const activityStatsQuery = useActivityStats({
    organizationId: organization?.data?.id ?? "",
  });
  const locationsQuery = useLocations({
    organizationId: organization?.data?.id ?? "",
  });

  // Get the actual projects data from the API
  const projects = activityQuery.data?.data ?? [];
  const locations = locationsQuery.data?.data ?? [];
  const stats = activityStatsQuery.data;

  const filteredData = useMemo(() => {
    return projects.filter((project) => {
      const matchesSearch =
        searchQuery === "" ||
        project.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        project.locations?.some(
          (loc: any) =>
            loc.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
            loc.city.toLowerCase().includes(searchQuery.toLowerCase()) ||
            loc.state.toLowerCase().includes(searchQuery.toLowerCase()),
        ) ||
        project.sublocations?.some((subloc: any) =>
          subloc.name.toLowerCase().includes(searchQuery.toLowerCase()),
        );

      const matchesStatus =
        statusFilter === "all" ||
        project.status === statusFilter ||
        (statusFilter === "active" && project.status === "active");

      const matchesLocation =
        locationFilter === "all" ||
        project.locations?.some((loc: any) => loc.id === locationFilter);

      const matchesSublocation =
        sublocationFilter === "all" ||
        project.sublocations?.some(
          (subloc: any) => subloc.id === sublocationFilter,
        );

      return (
        matchesSearch && matchesStatus && matchesLocation && matchesSublocation
      );
    });
  }, [projects, searchQuery, statusFilter, locationFilter, sublocationFilter]);

  // Get unique locations for filter from the locations query
  const allLocations = locations;

  // Loading state
  if (
    organization.isLoading ||
    activityQuery.isLoading ||
    locationsQuery.isLoading ||
    activityStatsQuery.isLoading ||
    !organization.data
  ) {
    return <PageLoader />;
  }

  return (
    <PageWrapper>
      <div className="flex items-center justify-between">
        <PageTitle>Activity Monitor</PageTitle>
      </div>
      <PageContent>
        {/* Stats Overview */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          <StatCard
            title="Active Projects"
            subTitle="Currently playing"
            stat={stats?.activeProjectsCount}
            icon={<IconPlayerPlay className="size-4 text-green-600" />}
            subIcon={<IconTrendingUp className="size-4" />}
            classNames={{
              borderColor: "border-l-green-500",
              textColor: "text-green-700",
              iconBackgroundColor: "bg-green-100 dark:bg-green-900",
            }}
          />
          <StatCard
            title="Approved Projects"
            subTitle="Ready for deployment"
            stat={stats?.approvedProjectsCount}
            icon={
              <CheckCircle className="size-4 text-blue-600 dark:text-blue-400" />
            }
            subIcon={<IconCalendarWeek className="size-4" />}
            classNames={{
              borderColor: "border-l-blue-500",
              textColor: "text-blue-700 dark:text-blue-400",
              iconBackgroundColor: "bg-blue-100 dark:bg-blue-900",
            }}
          />
          <StatCard
            title="Completed Projects"
            subTitle="Successfully finished"
            stat={stats?.completedProjectsCount}
            icon={
              <IconFolder className="size-4 text-orange-600 dark:text-orange-400" />
            }
            subIcon={<IconCalendarCheck className="size-4" />}
            classNames={{
              borderColor: "border-l-orange-500",
              textColor: "text-orange-700 dark:text-orange-400",
              iconBackgroundColor: "bg-orange-100 dark:bg-orange-900",
            }}
          />
          <StatCard
            title="Active Locations"
            subTitle={`${stats?.activeLocationsCount ?? 0} of ${stats?.totalLocations ?? 0} locations`}
            stat={`${stats?.locationUtilizationRate ?? 0}%`}
            icon={
              <IconLocation className="size-4 text-purple-600 dark:text-purple-400" />
            }
            subIcon={<IconLocation className="size-4" />}
            classNames={{
              borderColor: "border-l-purple-500",
              textColor: "text-purple-700 dark:text-purple-400",
              iconBackgroundColor: "bg-purple-100 dark:bg-purple-900",
            }}
          />
        </div>

        {/* Filters */}
        <Card className="mt-8 p-6">
          <div className="mb-6">
            <h3 className="text-xl font-semibold text-gray-900">
              Filter projects
            </h3>
            <p className="mt-1 text-sm text-gray-600">
              Find projects by project name, status, and location
            </p>
          </div>

          <div className="grid gap-6 lg:grid-cols-4">
            <div>
              <Input
                label="Search"
                placeholder="Search projects, locations..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                icon={<IconSearch size={16} />}
                className=""
              />
            </div>

            <div className="">
              <Label className="text-sm font-medium text-gray-700">
                Status
              </Label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="mt-[5px]">
                  <SelectValue placeholder="All statuses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="approved">Approved</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label className="text-sm font-medium text-gray-700">
                Location
              </Label>
              <Select
                value={locationFilter}
                onValueChange={(value) => {
                  setLocationFilter(value);
                  // Reset sublocation filter when location changes
                  if (value === "all") {
                    setSublocationFilter("all");
                  }
                }}
              >
                <SelectTrigger className="mt-[5px]">
                  <SelectValue placeholder="All locations" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Locations</SelectItem>
                  {allLocations.map((location) => (
                    <SelectItem key={location.id} value={location.id}>
                      {location.name} - {location.city}, {location.state}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label className="text-sm font-medium text-gray-700">
                Sublocation
              </Label>
              <Select
                value={sublocationFilter}
                onValueChange={setSublocationFilter}
                disabled={locationFilter === "all"}
              >
                <SelectTrigger className="mt-[5px]">
                  <SelectValue
                    placeholder={
                      locationFilter === "all"
                        ? "Select location first"
                        : "All sublocations"
                    }
                  />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Sublocations</SelectItem>
                  {locationFilter !== "all" &&
                    allLocations
                      .find((loc) => loc.id === locationFilter)
                      ?.sublocations?.map((sublocation) => (
                        <SelectItem key={sublocation.id} value={sublocation.id}>
                          {sublocation.name}
                        </SelectItem>
                      ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </Card>

        <div>
          {!isEmpty(filteredData) ? (
            <div className="mt-8 grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {filteredData.map((project) => (
                <ActivityCard
                  key={project.id}
                  project={project}
                  orgSlug={slug}
                />
              ))}
            </div>
          ) : (
            <div className="mt-20">
              <EmptyState
                title="No activity found"
                subtitle="No projects match your current filters."
                icon={<IconActivity size={40} />}
              />
            </div>
          )}
        </div>
      </PageContent>
    </PageWrapper>
  );
}
