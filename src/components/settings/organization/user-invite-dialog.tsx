import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON>alogHeader,
  DialogTitle,
  type DialogProps,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Roles } from "@/types/organization.types";
import { capitalizeFirstLetter } from "@/utils/capitalize-first-letter";
import { zodResolver } from "@hookform/resolvers/zod";
import { Controller, useForm } from "react-hook-form";
import { z } from "zod";

const schema = z.object({
  email: z
    .string({ required_error: "Email is required." })
    .email({ message: "Please enter a valid email." }),
  role: z.nativeEnum(Roles, {
    errorMap: () => ({ message: "Role is required." }),
  }),
});

type FormData = z.infer<typeof schema>;

interface Props extends DialogProps {
  onClose: () => void;
  onInvite: (data: FormData) => Promise<void>;
}

export function UserInviteDialog({ open, onClose, onInvite }: Props) {
  const {
    register,
    handleSubmit,
    control,
    formState: { errors, isSubmitting },
    reset,
  } = useForm<FormData>({
    resolver: zodResolver(schema),
    defaultValues: { role: "manager" },
  });

  const closeModal = () => {
    reset();
    onClose();
  };

  const onSubmit = async (data: FormData) => {
    await onInvite(data);
    closeModal();
  };

  return (
    <Dialog open={open} onOpenChange={closeModal}>
      <DialogContent className="sm:max-w-[465px]">
        <DialogHeader>
          <DialogTitle>Invite user</DialogTitle>
        </DialogHeader>
        <DialogDescription>
          Enter the user's email and select a role to send an invitation.
        </DialogDescription>
        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="space-y-5">
            <Input
              label="Email"
              {...register("email")}
              error={!!errors.email}
              errorMessage={errors.email?.message}
              autoFocus
            />
            <Controller
              name="role"
              control={control}
              render={({ field }) => (
                <div>
                  <Label>Role</Label>
                  <Select value={field.value} onValueChange={field.onChange}>
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder="Select a role" />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.values(Roles).map((role) => (
                        <SelectItem key={role} value={role}>
                          {capitalizeFirstLetter(role)}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.role && (
                    // TODO: make this a component
                    <p className="mt-1 text-sm text-red-500">
                      {errors.role.message}
                    </p>
                  )}
                </div>
              )}
            />
          </div>
          <DialogFooter className="mt-6">
            <Button variant="outline" onClick={closeModal} type="button">
              Cancel
            </Button>
            <Button loading={isSubmitting} type="submit">
              Send invite
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
