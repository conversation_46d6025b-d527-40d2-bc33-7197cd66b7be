"use client";

import { SettingsSection } from "@/components/settings/settings-section";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Loader } from "@/components/ui/loader";
import { Separator } from "@/components/ui/separator";
import { UploadButton } from "@/components/ui/upload-button";
import { env } from "@/env";
import {
  useOrganizationBySlug,
  useOrganizationUpdateMutation,
} from "@/queries/organization.queries";
import { useFileDeleteMutation } from "@/queries/storage.queries";
import { OrgUpdateSchema } from "@/schemas/organization.schemas";
import type { OrganizationUpdateData } from "@/types/organization.types";
import { IMAGE_MIME_TYPE } from "@/types/utility.types";
import { createImageUploadUrl } from "@/utils/create-image-upload-url";
import { zodResolver } from "@hookform/resolvers/zod";
import { IconExclamationCircle, IconTrash, IconX } from "@tabler/icons-react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { useForm } from "react-hook-form";

export function SettingsOrganizationView({ slug }: { slug: string }) {
  const router = useRouter();
  const [uploadError, setUploadError] = useState("");
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: zodResolver(OrgUpdateSchema),
  });

  const organization = useOrganizationBySlug(slug);

  const updateOrganizationMutation = useOrganizationUpdateMutation();
  const updateOrganizationLogoMutation = useOrganizationUpdateMutation();
  const deleteFileMutation = useFileDeleteMutation();

  const onSubmit = async (data: OrganizationUpdateData) => {
    await updateOrganizationMutation.mutateAsync(
      {
        id: organization.data?.id ?? "",
        name: data.name ?? organization.data?.name,
        slug: data.slug ?? slug,
      },
      {
        onSuccess: (data) => {
          const shouldRedirect = data.slug !== slug;
          if (shouldRedirect) {
            router.replace(`/${data.slug}/settings`);
          }
        },
      },
    );
  };

  const deleteCurrentOrganizationLogo = async (
    image: string | null | undefined,
  ) => {
    if (!image) return;

    if (!image.startsWith(env.NEXT_PUBLIC_S3_PUBLIC_BUCKET_URL)) return;

    const fileKey = `organizations/${organization.data?.id}/${image.split("/").pop()!}`;

    try {
      return await deleteFileMutation.mutateAsync({ fileKey });
    } catch (error) {
      console.log(error);
    }
  };

  const updateOrganizationLogo = async (fileKey: string) => {
    return await updateOrganizationMutation.mutateAsync({
      id: organization.data?.id ?? "",
      logo: createImageUploadUrl(fileKey),
    });
  };

  const removeOrganizationLogo = async () => {
    await deleteCurrentOrganizationLogo(organization?.data?.logo);
    await updateOrganizationLogoMutation.mutateAsync({
      id: organization.data?.id ?? "",
      logo: "",
    });
  };

  return (
    <div>
      {organization.isLoading && (
        <div className="flex items-center justify-center py-[300px]">
          <Loader />
        </div>
      )}

      {!organization.isLoading && (
        <div>
          <SettingsSection>
            <div>
              <h2 className="text-base leading-7 font-semibold">
                Organization Profile
              </h2>
              <p className="mt-1 text-[15px] leading-6 text-gray-500">
                Manage your organization profile details.
              </p>
            </div>

            <Card className="md:col-span-2">
              <div className="flex items-center justify-between border-b border-gray-200 bg-sidebar px-6 py-4">
                <p className="text-base leading-6 font-medium">
                  Your organization details
                </p>
              </div>

              {uploadError && (
                // TODO: make this a component
                <div className="relative mb-4 rounded-md bg-red-50 p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex">
                      <div className="shrink-0">
                        <IconExclamationCircle
                          className="h-5 w-5 text-red-400"
                          aria-hidden="true"
                        />
                      </div>
                      <div className="ml-3">
                        <h3 className="text-sm font-medium text-red-800">
                          Error
                        </h3>
                        <div className="mt-2 text-sm text-red-700">
                          <p>{uploadError}</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="absolute top-1 right-1">
                    <Button
                      size="icon"
                      variant="ghost"
                      className="h-7 w-7 text-red-500 hover:bg-red-100 hover:text-red-500"
                      onClick={() => setUploadError("")}
                    >
                      <IconX size={16} />
                    </Button>
                  </div>
                </div>
              )}

              <form className="p-6">
                <div className="grid grid-cols-1 gap-x-4 gap-y-6 sm:max-w-xl sm:grid-cols-6">
                  <div className="col-span-full flex items-center gap-x-8">
                    <Avatar className="h-24 w-24 flex-none rounded-lg object-cover">
                      <AvatarImage src={organization?.data?.logo ?? ""} />
                      <AvatarFallback className="text-2xl">
                        {organization?.data?.name?.charAt(0)}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="flex items-center gap-2">
                        <UploadButton
                          route="organizationLogoUpload"
                          accept={IMAGE_MIME_TYPE.join(",")}
                          variant="outline"
                          text="Change logo"
                          onBeforeUpload={async ({ file }) => {
                            // delete the old user image
                            await deleteCurrentOrganizationLogo(
                              organization?.data?.logo,
                            );

                            // rename the file
                            return new File(
                              [file],
                              `${organization?.data?.id}-${`${file.name}`}`,
                              {
                                type: file.type,
                              },
                            );
                          }}
                          onUploadComplete={async ({ file }) => {
                            await updateOrganizationLogo(file.objectKey);
                          }}
                          onUploadError={(error) => {
                            if (error.message) {
                              setUploadError(error.message);
                            }
                          }}
                        />
                        {organization?.data?.logo && (
                          <Button
                            variant="outline"
                            type="button"
                            leftIcon={
                              <IconTrash size={16} className="text-red-500" />
                            }
                            onClick={removeOrganizationLogo}
                            loading={
                              // deleteFileMutation.isPending ||
                              updateOrganizationLogoMutation.isPending
                            }
                          >
                            Remove
                          </Button>
                        )}
                      </div>
                      <p className="mt-2 text-xs leading-5 text-gray-400">
                        JPG, PNG or WEBP. 1MB max.
                      </p>
                    </div>
                  </div>

                  <div className="space-y-4 sm:col-span-full">
                    <Input
                      label="Organization name"
                      allowAutoComplete={false}
                      defaultValue={organization?.data?.name}
                      {...register("name")}
                      error={errors.name !== undefined}
                      errorMessage={errors?.name?.message}
                    />
                    <Input
                      label="Organization slug"
                      allowAutoComplete={false}
                      defaultValue={organization?.data?.slug ?? ""}
                      {...register("slug")}
                      error={errors.slug !== undefined}
                      errorMessage={errors?.slug?.message}
                    />
                  </div>
                </div>
              </form>
              <div className="border-t border-gray-200 p-6">
                <Button
                  type="button"
                  onClick={handleSubmit(onSubmit)}
                  loading={updateOrganizationMutation.isPending}
                >
                  Save changes
                </Button>
              </div>
            </Card>
          </SettingsSection>

          <Separator className="my-8" />

          <SettingsSection>
            <div>
              <h2 className="text-base leading-7 font-semibold text-red-600">
                Danger Zone
              </h2>
              <p className="mt-1 text-[15px] leading-6 text-gray-500">
                Be careful, some of these actions are not reversible.
              </p>
            </div>

            <Card className="divide-y divide-gray-200 md:col-span-2">
              <div className="flex items-center justify-between bg-sidebar px-6 py-4">
                <p className="text-base leading-6 font-medium">
                  Delete organization
                </p>
              </div>
              <div>
                <div className="p-6">
                  <p className="leading-6 text-gray-500">
                    Permanently delete your organization, and all associated
                    data with it. We will also cancel any associated
                    subscriptions. This action cannot be undone - please proceed
                    with caution.
                  </p>
                </div>
                <div className="border-t border-gray-200 p-6">
                  <Button
                    variant="destructive"
                    leftIcon={<IconTrash size={16} />}
                    // onClick={deleteAccountModalHandler.open}
                  >
                    Delete organization
                  </Button>
                </div>
              </div>
            </Card>
          </SettingsSection>
        </div>
      )}
    </div>
  );
}
