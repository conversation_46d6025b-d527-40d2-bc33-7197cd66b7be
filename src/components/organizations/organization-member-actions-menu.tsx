"use client";

import { OrganizationLeaveDialog } from "@/components/organizations/organization-leave-dialog";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useDialog } from "@/hooks/use-dialog";
import { cn } from "@/libs/utils";
import {
  useOrganizationMemberDeleteMutation,
  useOrganizationUpdateMemberRoleMutation,
} from "@/queries/organization.queries";
import { useUser } from "@/queries/user.queries";
import {
  Roles,
  type OrganizationMember,
  type Role,
} from "@/types/organization.types";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  IconDots,
  IconLogout2,
  IconTrash,
  IconUser,
} from "@tabler/icons-react";
import { useRouter } from "next/navigation";
import { Controller, useForm } from "react-hook-form";
import { z } from "zod";
import { DeleteDialog } from "../ui/delete-dialog";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  type DialogProps,
} from "../ui/dialog";
import { Label } from "../ui/label";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";

interface Props {
  member: OrganizationMember;
  userMemberRole?: string;
}

export function OrganizationMemberActionsMenu({
  member,
  userMemberRole,
}: Props) {
  const user = useUser();
  const router = useRouter();
  const [openDialog, openDialogHandlers] = useDialog();
  const [roleDialog, roleDialogHandlers] = useDialog();
  const [leaveDialog, leaveDialogHandlers] = useDialog();

  const isMe = member.user.id === user?.data?.id;

  const isAdminRole = userMemberRole === Roles.ADMIN;

  const deleteMutation = useOrganizationMemberDeleteMutation(
    member?.organizationId,
  );

  const handleDelete = async () => {
    await deleteMutation.mutateAsync({ memberId: member.id });
  };

  const handleLeave = async () => {
    await deleteMutation.mutateAsync({ memberId: member.id });
    router.push("/organizations");
  };

  return (
    <div>
      {member && (
        <div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="h-auto p-1.5 text-gray-400 data-[state=open]:bg-accent data-[state=open]:text-gray-900"
                disabled={!isMe && !isAdminRole}
              >
                <IconDots size={18} />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className={cn("w-[160px]")}>
              <>
                {isAdminRole && (
                  <DropdownMenuItem onClick={roleDialogHandlers.open}>
                    <IconUser className="mr-2 h-4 w-4" />
                    <span>Change role</span>
                  </DropdownMenuItem>
                )}

                {!isMe && isAdminRole && (
                  <DropdownMenuItem
                    className="!text-red-500 hover:!bg-red-500/5"
                    onClick={openDialogHandlers.open}
                  >
                    <IconTrash className="mr-2 h-4 w-4" />
                    <span>Remove</span>
                  </DropdownMenuItem>
                )}

                {isMe && (
                  <DropdownMenuItem
                    className="!text-red-500 hover:!bg-red-500/5"
                    onClick={leaveDialogHandlers.open}
                  >
                    <IconLogout2 className="mr-2 h-4 w-4" />
                    <span>Leave</span>
                  </DropdownMenuItem>
                )}
              </>
            </DropdownMenuContent>
          </DropdownMenu>

          <DeleteDialog
            title="member"
            open={openDialog}
            onClose={openDialogHandlers.close}
            onDelete={handleDelete}
            loading={deleteMutation.isPending}
          />

          <OrganizationLeaveDialog
            open={leaveDialog}
            onClose={leaveDialogHandlers.close}
            onDelete={handleLeave}
            loading={deleteMutation.isPending}
          />

          <ChangeRoleDialog
            open={roleDialog}
            onClose={roleDialogHandlers.close}
            member={member}
          />
        </div>
      )}
    </div>
  );
}

interface ChangeRoleDialogProps extends DialogProps {
  member: OrganizationMember;
  onClose: () => void;
}

type MemberRole = Role;

const memberRoleSchema = z.object({
  role: z.enum(["admin", "manager", "viewer"]),
});

function ChangeRoleDialog({ open, member, onClose }: ChangeRoleDialogProps) {
  const {
    handleSubmit,
    formState: { isSubmitting },
    control,
  } = useForm<z.infer<typeof memberRoleSchema>>({
    resolver: zodResolver(memberRoleSchema),
    defaultValues: {
      role: member.role as MemberRole,
    },
  });

  const updateMemberMutation = useOrganizationUpdateMemberRoleMutation(
    member.organizationId,
  );

  const closeModal = () => {
    onClose();
  };

  const onSubmit = async (data: z.infer<typeof memberRoleSchema>) => {
    await updateMemberMutation.mutateAsync({
      memberId: member.id,
      role: data.role as MemberRole,
    });
    closeModal();
  };

  return (
    <Dialog open={open} onOpenChange={closeModal}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Change user role</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="mt-3">
          <div>
            <Label>Role</Label>
            <div className="mt-2">
              <Controller
                name="role"
                control={control}
                render={({ field }) => (
                  <Select
                    defaultValue={member.role}
                    onValueChange={(value) => field.onChange(value)}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select a role" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectGroup>
                        <SelectItem value="admin">Admin</SelectItem>
                        <SelectItem value="manager">Manager</SelectItem>
                        <SelectItem value="viewer">Viewer</SelectItem>
                      </SelectGroup>
                    </SelectContent>
                  </Select>
                )}
              />
            </div>
          </div>

          <DialogFooter className="mt-6">
            <Button
              variant="outline"
              className="mt-2 sm:mt-0"
              onClick={closeModal}
              type="button"
            >
              Close
            </Button>
            <Button loading={isSubmitting} type="submit">
              Change role
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
