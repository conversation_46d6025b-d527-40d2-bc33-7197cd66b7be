"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogTitle,
  type DialogProps,
} from "@/components/ui/dialog";
import { UploadDropzoneProgress } from "@/components/ui/upload-dropzone-progress";
import { IMAGE_MIME_TYPE } from "@/types/utility.types";
import { IconExclamationCircle, IconX } from "@tabler/icons-react";
import type { UploadedFile } from "better-upload/client";
import { useState } from "react";

interface Props extends DialogProps {
  onUploadComplete: (files: UploadedFile[]) => Promise<void>;
  onClose: () => void;
  organizationId: string;
}

export function SlidesImageUploaderUpdate({
  onClose,
  open,
  onUploadComplete,
  organizationId,
}: Props) {
  const [uploadError, setUploadError] = useState("");

  function closeModal() {
    onClose();
    setUploadError("");
  }

  return (
    <Dialog open={open} onOpenChange={closeModal}>
      <DialogContent className="sm:max-w-[550px]" hideCloseButton={false}>
        <DialogTitle>Update Slide</DialogTitle>

        <div>
          {uploadError && (
            <div className="relative mb-4 rounded-md bg-red-50 p-4">
              <div className="flex items-start justify-between">
                <div className="flex">
                  <div className="shrink-0">
                    <IconExclamationCircle
                      className="h-5 w-5 text-red-400"
                      aria-hidden="true"
                    />
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-red-800">Error</h3>
                    <div className="mt-2 text-sm text-red-700">
                      <p>{uploadError}</p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="absolute top-1 right-1">
                <Button
                  size="icon"
                  variant="ghost"
                  className="h-7 w-7 text-red-500 hover:bg-red-100 hover:text-red-500"
                  onClick={() => setUploadError("")}
                >
                  <IconX size={16} />
                </Button>
              </div>
            </div>
          )}
          <section>
            <UploadDropzoneProgress
              route="singleSlideUpload"
              description={{
                maxFiles: 1,
                maxFileSize: "4MB",
                fileTypes: "JPEG, PNG, GIF, WEBP, AVIF",
              }}
              accept={IMAGE_MIME_TYPE.join(",")}
              onBeforeUpload={({ files }) => {
                setUploadError("");
                // rename all files
                return files.map(
                  (file) =>
                    new File([file], `${organizationId}-` + file.name, {
                      type: file.type,
                    }),
                );
              }}
              onUploadComplete={({ files }) => {
                onUploadComplete(files);
              }}
              onUploadError={(error) => {
                console.log("error", error);
                if (error.message) {
                  setUploadError(error.message);
                }
              }}
            />
          </section>
        </div>
      </DialogContent>
    </Dialog>
  );
}
