"use client";

import { But<PERSON> } from "@/components/ui/button";
import { DeleteDialog } from "@/components/ui/delete-dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useClipboard } from "@/hooks/use-clipboard";
import { useDialog } from "@/hooks/use-dialog";
import { useProjectViewDeleteMutation } from "@/queries/project.queries";
import { IconCopy, IconDots, IconTrash } from "@tabler/icons-react";
import { toast } from "sonner";

interface Props {
  disabled?: boolean;
  projectId: string;
}

export function ProjectViewActionsMenu({ disabled, projectId }: Props) {
  const [openDeleteDialog, openDeleteDialogHandlers] = useDialog();
  const clipboard = useClipboard();

  const deleteMutation = useProjectViewDeleteMutation();

  function onOpenDeleteDialog(e: React.SyntheticEvent) {
    e.stopPropagation();
    openDeleteDialogHandlers.open();
  }

  async function onDelete() {
    await deleteMutation.mutateAsync({ id: projectId });
  }

  function onCopy() {
    clipboard.copy(projectId);
    toast.success("Project Id copied to clipboard");
  }

  return (
    <div>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="icon"
            className="text-gray-400 data-[state=open]:bg-accent data-[state=open]:text-gray-900"
            title="Open project actions menu"
          >
            <IconDots size={18} />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-[180px]">
          <DropdownMenuItem onClick={onCopy}>
            <IconCopy className="mr-2 h-4 w-4" />
            <span>Copy project Id</span>
          </DropdownMenuItem>
          <DropdownMenuItem
            className="text-red-500! hover:bg-red-500/5!"
            onClick={onOpenDeleteDialog}
            disabled={disabled}
          >
            <IconTrash className="mr-2 h-4 w-4 text-red-500" />
            <span>Delete</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <DeleteDialog
        title="Project"
        open={openDeleteDialog}
        onClose={openDeleteDialogHandlers.close}
        onDelete={onDelete}
        loading={deleteMutation.isPending}
      />
    </div>
  );
}
