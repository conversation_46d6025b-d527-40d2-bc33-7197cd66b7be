"use client";

import type { Slide } from "@/types/slide.types";
import Image from "next/image";
import { useEffect, useRef, useState } from "react";

interface Props {
  slides: Pick<Slide, "id" | "imageUrl" | "order">[];
  autoPlayInterval?: number;
}

export function ProjectSlideshow({ slides, autoPlayInterval = 10000 }: Props) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // Reset index when slides array actually changes
  useEffect(() => {
    setCurrentIndex(0);
  }, [slides.length]);

  // Auto-play functionality with cleanup
  useEffect(() => {
    // Clear any existing interval
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    if (slides.length <= 1) return;

    intervalRef.current = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % slides.length);
    }, autoPlayInterval);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [slides.length, autoPlayInterval]);

  if (!slides.length) {
    return (
      <div className="flex h-screen items-center justify-center bg-black">
        <p className="text-4xl font-medium text-white">No slides to display</p>
      </div>
    );
  }

  return (
    <div className="relative h-screen w-full overflow-hidden bg-black">
      {/* Main slideshow container */}
      <div
        className="flex h-full w-full transition-transform duration-1000 ease-out"
        style={{ transform: `translateX(-${currentIndex * 100}%)` }}
      >
        {slides.map((slide, index) => (
          <div
            key={slide.id}
            className="relative flex h-full w-full flex-shrink-0 items-center justify-center p-16"
          >
            <div className="relative h-full w-full">
              <Image
                src={slide.imageUrl}
                alt={`Slide ${index + 1}`}
                className="h-full w-full object-contain"
                loading={index === 0 ? "eager" : "lazy"}
                fill
              />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
