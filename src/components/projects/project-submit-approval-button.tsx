"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { useSubmitForApproval } from "@/queries/approval.queries";
import { useState } from "react";

interface ProjectSubmitApprovalButtonProps {
  projectId: string;
  projectName: string;
  disabled?: boolean;
  startDate?: Date | null;
  endDate?: Date | null;
  selectedLocations?: Set<string>;
  selectedSublocations?: Set<string>;
}

export function ProjectSubmitApprovalButton({
  projectId,
  projectName,
  disabled = false,
  startDate,
  endDate,
  selectedLocations,
  selectedSublocations,
}: ProjectSubmitApprovalButtonProps) {
  const [open, setOpen] = useState(false);
  const [comments, setComments] = useState("");
  const [error, setError] = useState("");

  const submitMutation = useSubmitForApproval();

  const validateSubmission = () => {
    if (startDate && endDate && startDate > endDate) {
      return {
        error: true,
        errorMessage: "Start date must be before end date",
      };
    }
    return { error: false, errorMessage: "" };
  };

  const handleSubmit = () => {
    setError("");

    const validation = validateSubmission();
    if (validation.error) {
      setError(validation.errorMessage);
      return;
    }

    const submitData: any = {
      projectId,
      comments: comments || undefined,
    };

    if (startDate) {
      submitData.startDate = startDate.toISOString();
    }
    if (endDate) {
      submitData.endDate = endDate.toISOString();
    }
    if (selectedLocations && selectedLocations.size > 0) {
      submitData.locations = Array.from(selectedLocations);
    }
    if (selectedSublocations && selectedSublocations.size > 0) {
      submitData.sublocations = Array.from(selectedSublocations);
    }

    submitMutation.mutate(submitData, {
      onSuccess: () => {
        setOpen(false);
        setComments("");
        setError("");
      },
    });
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button disabled={disabled}>Submit for Approval</Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Submit for Approval</DialogTitle>
          <DialogDescription>
            Submit "{projectName}" for approval by your organization
            administrators.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {error && (
            <div className="rounded-lg border border-red-200 bg-red-50 p-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          )}
          <div className="space-y-2">
            <Textarea
              label="Comments (Optional)"
              placeholder="Add any comments or notes for the reviewers..."
              value={comments}
              onChange={(e) => setComments(e.target.value)}
              rows={4}
            />
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => setOpen(false)}
            disabled={submitMutation.isPending}
          >
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={submitMutation.isPending}>
            {submitMutation.isPending ? "Submitting..." : "Submit for Approval"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
