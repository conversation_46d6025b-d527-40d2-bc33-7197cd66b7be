"use client";

import { But<PERSON> } from "@/components/ui/button";
import { DeleteDialog } from "@/components/ui/delete-dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useDialog } from "@/hooks/use-dialog";
import { useProjectDeleteMutation } from "@/queries/project.queries";
import {
  IconDots,
  IconPencil,
  IconPlayerPlay,
  IconTrash,
} from "@tabler/icons-react";
import Link from "next/link";

interface Props {
  disabled?: boolean;
  projectId: string;
  organizationSlug: string;
}

export function ProjectCardActionsMenu({
  disabled,
  projectId,
  organizationSlug,
}: Props) {
  const [openDeleteDialog, openDeleteDialogHandlers] = useDialog();

  const deleteMutation = useProjectDeleteMutation();

  function onOpenDeleteDialog(e: React.SyntheticEvent) {
    e.stopPropagation();
    openDeleteDialogHandlers.open();
  }

  async function onDelete() {
    await deleteMutation.mutateAsync({ id: projectId });
  }

  return (
    <div>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="icon"
            className="size-7 text-gray-400 data-[state=open]:bg-accent data-[state=open]:text-gray-900"
            title="Open project actions menu"
          >
            <IconDots size={18} />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-[180px]">
          <Link href={`/${organizationSlug}/projects/${projectId}`}>
            <DropdownMenuItem>
              <IconPencil className="mr-2 h-4 w-4" />
              <span>Edit</span>
            </DropdownMenuItem>
          </Link>
          <Link
            href={`/secure/preview?projectId=${projectId}`}
            target="_blank"
            rel="noreferrer noopener"
          >
            <DropdownMenuItem>
              <IconPlayerPlay className="mr-2 h-4 w-4" />
              <span>Preview</span>
            </DropdownMenuItem>
          </Link>

          <DropdownMenuItem
            className="text-red-500! hover:bg-red-500/5!"
            onClick={onOpenDeleteDialog}
            disabled={disabled}
          >
            <IconTrash className="mr-2 h-4 w-4 text-red-500" />
            <span>Delete</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <DeleteDialog
        title="Project"
        open={openDeleteDialog}
        onClose={openDeleteDialogHandlers.close}
        onDelete={onDelete}
        loading={deleteMutation.isPending}
      />
    </div>
  );
}
