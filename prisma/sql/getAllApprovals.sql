SELECT 
  pa.id,
  pa.projectId,
  pa.requesterId,
  pa.approverId,
  pa.status,
  pa.submittedAt,
  pa.reviewedAt,
  pa.comments,
  pa.reviewComments,
  pa.createdAt,
  pa.updatedAt,
  p.id as project_id,
  p.name as project_name,
  p.status as project_status,
  (SELECT COUNT(*) FROM slides s WHERE s.projectId = p.id) as slide_count,
  req.id as requester_id,
  req.name as requester_name,
  req.email as requester_email,
  req.image as requester_image,
  app.id as approver_id,
  app.name as approver_name,
  app.email as approver_email,
  app.image as approver_image
FROM project_approvals pa
INNER JOIN projects p ON pa.projectId = p.id
INNER JOIN user req ON pa.requesterId = req.id
LEFT JOIN user app ON pa.approverId = app.id
INNER JOIN (
  SELECT 
    projectId,
    MAX(createdAt) as max_created
  FROM project_approvals
  GROUP BY projectId
) latest ON pa.projectId = latest.projectId AND pa.createdAt = latest.max_created
WHERE p.organizationId = ?
  AND (
    (p.status = 'pending_approval' AND pa.status = 'pending') OR
    (p.status = 'approved' AND pa.status = 'approved')
  )
  AND (? IS NULL OR ? = '' OR p.name LIKE CONCAT('%', ?, '%'))
  AND (? IS NULL OR ? = 'all' OR pa.status = ?)
  AND (? IS NULL OR pa.projectId = ?)
  AND (? IS NULL OR pa.createdAt < (SELECT createdAt FROM project_approvals WHERE id = ?))
ORDER BY pa.createdAt DESC
LIMIT ?;